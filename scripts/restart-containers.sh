#!/bin/bash

# Ukshati 2.0 Container Restart Script
# This script restarts the containers with the new MySQL configuration

echo "🔄 Restarting Ukshati 2.0 containers..."
echo "======================================"
echo ""

echo "📦 Stopping existing containers..."
docker-compose down

echo ""
echo "🧹 Cleaning up old containers and images..."
docker-compose rm -f
docker system prune -f

echo ""
echo "🏗️  Building and starting containers with new configuration..."
docker-compose up --build -d

echo ""
echo "⏳ Waiting for services to start..."
sleep 10

echo ""
echo "🔍 Checking container status..."
docker-compose ps

echo ""
echo "📊 Checking database logs..."
docker-compose logs db | tail -10

echo ""
echo "✅ Restart complete!"
echo ""
echo "🧪 Test your backup system:"
echo "   1. Go to: http://localhost:3000/backup/test"
echo "   2. Click 'Run Tests'"
echo "   3. Verify all tests pass"
echo ""
echo "🔧 If issues persist:"
echo "   1. Check logs: docker-compose logs"
echo "   2. Restart again: ./scripts/restart-containers.sh"
echo "   3. Check environment: cat .env"
echo ""
