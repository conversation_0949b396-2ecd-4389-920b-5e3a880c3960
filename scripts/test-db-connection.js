#!/usr/bin/env node

/**
 * Test database connection
 */

const mysql = require('mysql2/promise');

async function testDatabaseConnection() {
  console.log('🔌 Testing database connection...');

  const configs = [
    {
      name: 'Docker Database',
      host: 'localhost',
      port: 3306,
      user: 'company',
      password: 'Ukshati@123',
      database: 'company_db'
    },
    {
      name: 'Local Database',
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'company_db'
    }
  ];

  for (const config of configs) {
    try {
      console.log(`\n📡 Testing ${config.name}...`);
      console.log(`   Host: ${config.host}:${config.port}`);
      console.log(`   User: ${config.user}`);
      console.log(`   Database: ${config.database}`);

      const connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        database: config.database,
        connectTimeout: 5000
      });

      // Test the connection
      const [rows] = await connection.execute('SELECT 1 as test');
      console.log(`✅ ${config.name} connection successful!`);
      
      // Check if backup tables exist
      const [tables] = await connection.execute(`
        SHOW TABLES LIKE 'backup_%'
      `);
      console.log(`   Found ${tables.length} backup tables`);

      await connection.end();
      return config; // Return the working config
    } catch (error) {
      console.log(`❌ ${config.name} connection failed: ${error.message}`);
    }
  }

  throw new Error('No database connection available');
}

if (require.main === module) {
  testDatabaseConnection()
    .then((config) => {
      console.log(`\n🎉 Database connection test completed successfully with ${config.name}`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Database connection test failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testDatabaseConnection };
