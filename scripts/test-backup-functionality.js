#!/usr/bin/env node

/**
 * Test script for backup functionality
 * This script tests the backup system without requiring the full web application
 */

const path = require('path');
const fs = require('fs');

// Set up environment
process.env.NODE_ENV = 'development';
process.env.DB_HOST = 'localhost';
process.env.DB_USER = 'company';
process.env.DB_PASSWORD = 'Ukshati@123';
process.env.DB_NAME = 'company_db';

// Import modules
const backupService = require('../frontend/lib/backupService.js').default;
const backupScheduler = require('../frontend/lib/backupScheduler.js').default;

async function testBackupFunctionality() {
  console.log('🧪 Starting backup functionality tests...\n');

  try {
    // Test 1: Initialize backup tables
    console.log('📋 Test 1: Initialize backup tables');
    const { connectToDB } = require('../frontend/lib/db.js');
    const db = await connectToDB();
    await backupService.ensureBackupTables(db);
    db.release();
    console.log('✅ Backup tables initialized successfully\n');

    // Test 2: Test backup scheduler initialization
    console.log('⏰ Test 2: Initialize backup scheduler');
    await backupScheduler.initialize();
    console.log('✅ Backup scheduler initialized successfully\n');

    // Test 3: Test backup settings save
    console.log('⚙️ Test 3: Save backup settings');
    const testSettings = {
      frequency: 'daily',
      time: '03:00',
      dayOfWeek: 1,
      dayOfMonth: 1,
      enabled: true,
      folderId: null
    };
    await backupScheduler.saveBackupSettings('<EMAIL>', testSettings);
    console.log('✅ Backup settings saved successfully\n');

    // Test 4: Test backup settings retrieval
    console.log('📖 Test 4: Retrieve backup settings');
    const retrievedSettings = await backupScheduler.getBackupSettings('<EMAIL>');
    console.log('Retrieved settings:', retrievedSettings);
    console.log('✅ Backup settings retrieved successfully\n');

    // Test 5: Test backup history retrieval
    console.log('📚 Test 5: Retrieve backup history');
    const backupHistory = await backupService.getBackupHistory(10);
    console.log(`Found ${backupHistory.length} backup records`);
    if (backupHistory.length > 0) {
      console.log('Sample record:', backupHistory[0]);
    }
    console.log('✅ Backup history retrieved successfully\n');

    // Test 6: Test manual backup creation (if database is available)
    console.log('💾 Test 6: Create manual backup');
    try {
      const backupResult = await backupService.performFullBackup('manual');
      console.log('Backup created:', {
        id: backupResult.id,
        name: backupResult.name,
        size: backupResult.size
      });
      console.log('✅ Manual backup created successfully\n');
    } catch (error) {
      console.log('⚠️ Manual backup failed (this is expected if database is not running):', error.message);
      console.log('');
    }

    // Test 7: Test backup history after creation
    console.log('📚 Test 7: Check backup history after creation');
    const updatedHistory = await backupService.getBackupHistory(10);
    console.log(`Found ${updatedHistory.length} backup records after creation`);
    if (updatedHistory.length > 0) {
      console.log('Latest record:', updatedHistory[0]);
    }
    console.log('✅ Backup history check completed\n');

    console.log('🎉 All backup functionality tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run tests
if (require.main === module) {
  testBackupFunctionality()
    .then(() => {
      console.log('\n✨ Test suite completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testBackupFunctionality };
