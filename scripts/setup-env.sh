#!/bin/bash

# Ukshati 2.0 Backup System Environment Setup Script
# This script helps you configure the environment variables needed for the backup system

echo "🚀 Ukshati 2.0 Backup System Setup"
echo "=================================="
echo ""

# Check if .env file exists
if [ -f .env ]; then
    echo "📄 Found existing .env file"
    read -p "Do you want to update it? (y/n): " update_env
    if [ "$update_env" != "y" ]; then
        echo "Setup cancelled."
        exit 0
    fi
    echo ""
else
    echo "📄 Creating new .env file from template..."
    cp .env.example .env
    echo "✅ .env file created"
    echo ""
fi

echo "🔧 Google OAuth Configuration"
echo "-----------------------------"
echo "To enable Google Drive backup, you need to:"
echo "1. Go to Google Cloud Console: https://console.cloud.google.com/"
echo "2. Create a new project or select existing one"
echo "3. Enable Google Drive API"
echo "4. Create OAuth 2.0 credentials"
echo "5. Add redirect URI: http://localhost:3000/api/auth/google/callback"
echo ""

read -p "Enter your Google Client ID (or press Enter to skip): " google_client_id
read -p "Enter your Google Client Secret (or press Enter to skip): " google_client_secret

if [ ! -z "$google_client_id" ] && [ ! -z "$google_client_secret" ]; then
    # Update .env file with Google credentials
    sed -i "s/GOOGLE_CLIENT_ID=.*/GOOGLE_CLIENT_ID=$google_client_id/" .env
    sed -i "s/GOOGLE_CLIENT_SECRET=.*/GOOGLE_CLIENT_SECRET=$google_client_secret/" .env
    echo "✅ Google OAuth credentials configured"
else
    echo "⚠️  Google OAuth credentials skipped - you can configure them later"
fi

echo ""
echo "🌐 Application URL Configuration"
echo "--------------------------------"
read -p "Enter your application URL (default: http://localhost:3000): " app_url
app_url=${app_url:-http://localhost:3000}

# Update .env file with app URL
sed -i "s|NEXT_PUBLIC_APP_URL=.*|NEXT_PUBLIC_APP_URL=$app_url|" .env
sed -i "s|GOOGLE_REDIRECT_URI=.*|GOOGLE_REDIRECT_URI=$app_url/api/auth/google/callback|" .env
echo "✅ Application URL configured: $app_url"

echo ""
echo "📊 Database Configuration"
echo "------------------------"
echo "Current database settings:"
echo "- Host: db (Docker container)"
echo "- User: company"
echo "- Password: Ukshati@123"
echo "- Database: company_db"
echo ""
read -p "Do you want to change database settings? (y/n): " change_db
if [ "$change_db" = "y" ]; then
    read -p "Database Host (default: db): " db_host
    read -p "Database User (default: company): " db_user
    read -p "Database Password (default: Ukshati@123): " db_password
    read -p "Database Name (default: company_db): " db_name
    
    db_host=${db_host:-db}
    db_user=${db_user:-company}
    db_password=${db_password:-Ukshati@123}
    db_name=${db_name:-company_db}
    
    # Update .env file with database settings
    sed -i "s/DB_HOST=.*/DB_HOST=$db_host/" .env
    sed -i "s/DB_USER=.*/DB_USER=$db_user/" .env
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$db_password/" .env
    sed -i "s/DB_NAME=.*/DB_NAME=$db_name/" .env
    echo "✅ Database settings updated"
else
    echo "✅ Using default database settings"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Start the application: docker-compose up --build"
echo "2. Go to: $app_url/backup/settings"
echo "3. Connect your Google Drive account"
echo "4. Configure backup settings"
echo "5. Test the system: $app_url/backup/test"
echo ""

if [ -z "$google_client_id" ] || [ -z "$google_client_secret" ]; then
    echo "⚠️  Note: Google Drive backup is not configured yet."
    echo "   You can still use local backups, but for cloud backup:"
    echo "   1. Set up Google OAuth credentials in Google Cloud Console"
    echo "   2. Update GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in .env"
    echo "   3. Restart the application"
    echo ""
fi

echo "📖 For detailed setup instructions, see: BACKUP_SETUP.md"
echo ""
echo "Happy backing up! 🚀"
