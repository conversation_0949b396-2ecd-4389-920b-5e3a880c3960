# 🚀 Backup System Quick Start

Get your Google Drive backup system up and running in 5 minutes!

## ⚡ Quick Setup

### 1. Run Setup Script
```bash
./scripts/setup-env.sh
```

### 2. Start Application
```bash
docker-compose up --build
```

### 3. Configure Backup
1. Go to: http://localhost:3000/backup/settings
2. Click "Connect Google Drive"
3. Authorize with your Google account
4. Configure backup schedule
5. Test with "Create Backup Now"

## 🧪 Test Your Setup

Visit: http://localhost:3000/backup/test

This will run comprehensive tests to verify:
- ✅ Database connection
- ✅ Backup creation
- ✅ Google Drive authentication
- ✅ Cloud upload functionality

## 🔧 Manual Configuration (if needed)

### Google Cloud Console Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create/select project
3. Enable Google Drive API
4. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URI: `http://localhost:3000/api/auth/google/callback`
5. Copy Client ID and Client Secret

### Environment Variables
Update `.env` file:
```env
GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/google/callback
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🎯 Features

- **One-Click Google Drive Connection** - No manual file configuration
- **Automatic Scheduling** - Daily, weekly, or monthly backups
- **Real-time Status** - Dashboard widget shows backup status
- **Easy Management** - View, restore, and delete backups
- **Comprehensive Testing** - Built-in system diagnostics
- **Fallback Support** - Works with both MariaDB and MySQL

## 🔍 Troubleshooting

### Common Issues

**"mariadb-dump not found"**
- ✅ System automatically falls back to mysqldump
- No action needed

**"Google Drive not connected"**
- ✅ Local backups still work
- Connect Google Drive for cloud backups

**"Database connection failed"**
- Check database is running: `docker-compose ps`
- Verify credentials in `.env` file

**"Dialog won't close"**
- ✅ Fixed in latest version
- Dialogs now auto-close or have proper buttons

### Test Everything
```bash
# Visit test page
http://localhost:3000/backup/test

# Check logs
docker-compose logs frontend

# Manual backup test
curl -X POST http://localhost:3000/api/backup/create
```

## 📱 User Experience

### Dashboard Integration
- Backup status widget on main dashboard
- Quick access to settings and management
- Real-time backup progress

### Settings Page
- Email configuration
- Backup frequency selection
- Google Drive connection status
- One-click backup creation

### Management Page
- View all backups
- Restore from any backup
- Delete old backups
- Storage usage monitoring

## 🚀 Production Deployment

1. Update environment variables for production:
   ```env
   NEXT_PUBLIC_APP_URL=https://yourdomain.com
   GOOGLE_REDIRECT_URI=https://yourdomain.com/api/auth/google/callback
   ```

2. Update Google Cloud Console:
   - Add production redirect URI
   - Update authorized domains

3. Deploy and test:
   ```bash
   docker-compose up -d
   ```

## 📞 Support

- **Test Page**: `/backup/test` - Comprehensive system diagnostics
- **Settings**: `/backup/settings` - Configure and test backups
- **Management**: `/backup/manage` - View and manage backups
- **Documentation**: `BACKUP_SETUP.md` - Detailed setup guide

---

**Ready to backup? Start with the test page to verify everything works! 🎉**
