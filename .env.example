# Database Configuration
DB_HOST=db
DB_USER=company
DB_PASSWORD=Ukshati@123
DB_NAME=company_db

# Google OAuth Configuration for Backup System
# Get these from Google Cloud Console: https://console.cloud.google.com/
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/google/callback

# Application URL (used for OAuth redirects)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# MySQL/MariaDB Configuration
MYSQL_ROOT_PASSWORD=root123
MYSQL_DATABASE=company_db
MYSQL_USER=company
MYSQL_PASSWORD=Ukshati@123
