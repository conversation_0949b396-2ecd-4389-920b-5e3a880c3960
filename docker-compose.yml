services:
  nextjs:
    build: .
    ports:
      - "3000:3000"
    env_file:
      - .env
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - ./config/service-account.json:/app/config/service-account.json:ro
    environment:
      - NODE_ENV=development
      - DB_HOST=db
      - DB_USER=company
      - DB_PASSWORD=Ukshati@123
      - DB_NAME=company_db
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL:-http://localhost:3000}
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app-network

  db:
    build:
      context: .
      dockerfile: Dockerfile.mysql
    image: custom-mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: company_db
      MYSQL_USER: company
      MYSQL_PASSWORD: Ukshati@123
    volumes:
      - mysql-data:/var/lib/mysql
      - ./db:/docker-entrypoint-initdb.d
      - ./backups:/backups
    networks:
      - app-network
    healthcheck:
      test: [ "CMD-SHELL", "mysqladmin ping -u company --password=$$MYSQL_PASSWORD" ]
      interval: 5s
      timeout: 10s
      retries: 10

volumes:
  mysql-data:
  backups:


networks:
  app-network:
    driver: bridge
